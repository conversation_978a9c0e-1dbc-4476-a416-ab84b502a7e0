import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';
import { FlexContainer } from './containers';

// =============================================================================
// LAYOUT SECTION COMPONENTS
// =============================================================================

/**
 * Section wrapper with consistent spacing
 */
export const Section = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
}));

/**
 * Page header with title and actions
 */
export const PageHeader = styled(FlexContainer)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(2),
  },
}));

/**
 * Empty state container
 */
export const EmptyState = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(6, 3),
  color: theme.palette.text.secondary,
}));
