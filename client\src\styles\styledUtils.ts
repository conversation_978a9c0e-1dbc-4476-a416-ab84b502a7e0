// =============================================================================
// DEPRECATED: This file is deprecated in favor of the new modular structure
// =============================================================================
//
// ⚠️  DEPRECATION NOTICE:
// This file has been refactored into a modular structure for better maintainability.
//
// NEW IMPORT PATHS:
// - Layout components: import from '@/styles/components/layout'
// - Card components: import from '@/styles/components/cards'
// - Typography components: import from '@/styles/components/typography'
// - Utility functions: import from '@/styles/utils'
// - Types: import from '@/styles/types'
// - Or use the main export: import from '@/styles'
//
// This file will be removed in a future version. Please migrate to the new structure.
// =============================================================================

// Re-export types from new modular structure
export type {
  SpacingProps,
  VariantProps,
  PriorityProps,
  SizeProps,
} from './types/props';

// Re-export layout components from new modular structure
export {
  StyledContainer,
  FlexContainer,
  GridContainer,
  Section,
  PageHeader,
  EmptyState,
} from './components/layout';

// Re-export card components from new modular structure
export {
  StyledCard,
  StatCard,
  StatCardContent,
} from './components/cards';

// Re-export typography components from new modular structure
export {
  StyledTypography,
  IconTypography,
} from './components/typography';

// Re-export utility functions from new modular structure
export {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing,
} from './utils';

// Note: Section, PageHeader, and EmptyState are already exported above from './components/layout'
